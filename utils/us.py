import re
from fastapi.responses import J<PERSON><PERSON>esponse
from prompt import US_INDEX, Sector_prompt, STACK_OPINION
from LLM import OpenAiLLM
from format import UsIndex , StrongWeekStack, Opinion, TitleModel, US_NEWS_SUM
import asyncio
from googlesearch import search
from urllib.parse import urlparse
import tldextract
from crawl4ai import AsyncWebCrawler
from pydantic import BaseModel, Field
import os
import pandas as pd
import requests
from typing import List


def extract_and_clean_focus_stocks(txt_lines):
    # 找到《個股焦點》所在的 index
    try:
        start_index = txt_lines.index('《個股焦點》') + 1
    except ValueError:
        return []

    # 取接下來4行
    focus_lines = txt_lines[start_index:]

    cleaned_lines = []
    for line in focus_lines:
        # 如果第一個字不是中文、英文、數字，就去掉
        if not re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9]', line):
            line = line[1:]
        cleaned_lines.append(line)

    return cleaned_lines

DOMAIN_TO_CHINESE = {
    'money.udn.com': '聯合新聞',
    'udn.com': '聯合新聞',
    'www.cnyes.com': '鉅亨',
    'news.cnyes.com': '鉅亨',
    'cnyes.com': '鉅亨',
    'tw.stock.yahoo.com': 'Yahoo',
    'yahoo.com': 'Yahoo',
    'news.yahoo.com': 'Yahoo',
    'www.moneydj.com': 'MoneyDJ',
    'moneydj.com': 'MoneyDJ',
    'ctee.com.tw': '工商時報',
    'money.ctee.com.tw': '工商時報',
    'money.udn.com': '經濟日報',
    'money.sina.com.tw': '經濟日報',
    'www.chinatimes.com': '中時新聞',
    'ec.ltn.com.tw': '自由時報',
    'www.ltn.com.tw': '自由時報',
    'www.ettoday.net': 'ETtoday',
    'finance.ettoday.net': 'ETtoday',
    'www.setn.com': '三立新聞',
    'www.storm.mg': '風傳媒',
    'www.nownews.com': 'NOWnews',
    'www.anuefund.com': '鉅亨',
    'www.businesstoday.com.tw': '今周刊',
    'www.businessweekly.com.tw': '商業周刊',
    'www.cmoney.tw': 'CMoney',
    'www.mirrormedia.mg': '鏡週刊',
    'vocus.cc': 'Vocus',
    'aastocks.com': 'AAStocks',
    'tvb.com': 'TVBS'
}

def get_chinese_source(domain):
    """將完整 domain 轉換為對應中文名稱或主網域"""
    if domain in DOMAIN_TO_CHINESE:
        return DOMAIN_TO_CHINESE[domain]

    # 萃取主網域（如 www.ctee.com.tw → ctee.com.tw）
    ext = tldextract.extract(domain)
    root_domain = f"{ext.domain}.{ext.suffix}" if ext.suffix else domain
    return DOMAIN_TO_CHINESE.get(root_domain, root_domain)

def is_us_stock_related(title, description=""):
    """
    判斷新聞是否真的與美股相關
    """
    # 美股相關的正面關鍵字
    us_keywords = [
        '美股', '那斯達克', 'NASDAQ', '道瓊', '標普', 'S&P', 'NYSE', '紐約證交所',
        '美國股市', '華爾街', 'Wall Street', '美聯儲', 'Fed', '聯準會',
        # 主要美股公司
        '蘋果', 'Apple', 'AAPL', '微軟', 'Microsoft', 'MSFT',
        '亞馬遜', 'Amazon', 'AMZN', '谷歌', 'Google', 'GOOGL',
        '特斯拉', 'Tesla', 'TSLA', '輝達', 'NVIDIA', 'NVDA',
        'Meta', 'Facebook', 'META'
    ]

    # 檢查標題和描述是否包含美股相關關鍵字
    content = f"{title} {description}".lower()
    return any(keyword.lower() in content for keyword in us_keywords)

def search_first_url(query, max_results=10):
    """
    使用 advanced search 回傳符合指定來源的結果清單，專注於美股相關新聞
    """
    # 專注於美股新聞的可靠來源，排除容易有港股、陸股新聞的來源
    # 移除容易混雜亞洲股市新聞的來源，專注於美股報導較純粹的媒體
    allowed_sources = {'經濟日報', '工商時報', 'CMoney', '鉅亨', 'Yahoo','MoneyDJ'}

    # 排除關鍵字，避免抓到香港、中國市場新聞
    exclude_keywords = [
        # 港股相關
        '港股', '恆生', '恆指', '港交所', '港幣', '香港', 'HK', 'HSI',
        # 中國股市相關
        '上證', '深證', '滬深', 'A股', '上交所', '深交所', '中國大陸', '內地', '陸股',
        '中概股', '人民幣', '滬港通', '深港通', '科創板', '創業板', '新三板',
        # 其他亞洲市場
        '日經', '韓股', '台股', '新加坡', '泰股', '印度股市',
        # 明確的地區標識
        '中國', '大陸', '亞洲', '亞太', '東南亞'
    ]

    results = []

    try:
        # 在搜尋查詢中加入美股相關關鍵字，提高相關性
        enhanced_query = f"{query} 美股"

        for result in search(enhanced_query, num_results=max_results, lang="zh-TW", advanced=True):
            domain = urlparse(result.url).netloc
            source = get_chinese_source(domain)

            # 檢查是否為允許的來源
            if source not in allowed_sources:
                continue

            # 檢查標題、描述和URL是否包含排除關鍵字
            title = result.title or ""
            description = result.description or ""
            url = result.url or ""

            # 如果標題、描述或URL包含排除關鍵字，跳過
            if any(keyword in title or keyword in description or keyword in url for keyword in exclude_keywords):
                continue

            # 檢查是否真的與美股相關
            if not is_us_stock_related(title, description):
                continue

            results.append({
                'title': title,
                'url': result.url,
                'source': source,
                'domain': domain,
                'description': description
            })

        return results if results else None

    except Exception as e:
        print(f"搜尋錯誤: {e}")
        return None


# async def crawl_content(url):
#     """使用 crawl4ai 抓取網頁內容"""
#     try:
#         async with AsyncWebCrawler() as crawler:
#             result = await crawler.arun(url=url)
#             return result.markdown
#     except Exception as e:
#         print(f"抓取內容錯誤: {e}")
#         return None

async def crawl_content(url):
    """使用 crawl4ai 抓取網頁內容，若第一次失敗則重試一次"""
    # 檢查 URL 是否有效
    if not url or not isinstance(url, str) or not url.strip():
        print(f"無效的 URL: {url}")
        return None

    # 確保 URL 以 http:// 或 https:// 開頭
    url = url.strip()
    if not url.startswith(('http://', 'https://')):
        print(f"URL 格式不正確: {url}")
        return None

    for attempt in range(2):  # 最多嘗試兩次
        try:
            async with AsyncWebCrawler() as crawler:
                result = await crawler.arun(url=url)
                return result.markdown
        except Exception as e:
            print(f"第 {attempt + 1} 次抓取內容錯誤: {e}")
            if attempt == 1:  # 第二次也失敗
                return None


def transform1(data):
    output = []

    # 固定資訊
    output.append({
        "type": "text",
        "value": data.get("report_date", ""),
        "x": 285,
        "y": 50,
        "fontSize": 24,
        "fill": "#00367c"
    })

    # output.append({
    #     "type": "text",
    #     "value": data["title"],
    #     "x": 152,
    #     "y": 110,
    #     "fontSize": 57,
    #     "fill": "#FFFFFF"
    # })

    output.append({
        "type": "text",
        "value": data.get("previous_date", "")+'指數',
        "x": 189,
        "y": 147,
        "fontSize": 15,
        "fill": "#00367c"
    })

    def get_color(trend):
        return "#02b317" if trend == "us_up" else "#da0000"

    def convert_trend(trend):
        return "us_up" if trend == "up" else "us_down"
    
    def format_percent_change(percent_change_raw, point_trend):
        """
        格式化 percent_change，確保加上正負號，並保持兩位小數格式。

        :param percent_change_raw: 原始 percent change，可能是 float 或 str
        :param point_trend: 用來判斷方向的原始趨勢，如 "up"、"down"
        :return: 已加正負號的字串，例如 "+0.70"
        """
        # 轉為字串並清理空白與 % 號
        percent_str = str(percent_change_raw).replace('%', '').strip()

        try:
            # 轉換為浮點數以確保格式化
            percent_float = float(percent_str.lstrip('+-'))

            # 根據 point_trend 判斷要補 + 還是 -
            trend = convert_trend(point_trend)
            sign = '+' if trend == "us_up" else '-'

            # 格式化為兩位小數
            return f"{sign}{percent_float:.2f}"

        except ValueError:
            # 如果轉換失敗，使用原來的邏輯
            if percent_str.startswith(('+', '-')):
                return percent_str

            trend = convert_trend(point_trend)
            sign = '+' if trend == "us_up" else '-'
            return f"{sign}{percent_str}"


    # dow_jones_index
    dj = data["dow_jones_index"]
    output.extend([
        {
            "type": "text",
            "value": str(dj["current_value"]),
            "x": 134,
            "y": 226,
            "fontSize": 26,
            "fill": "#00000"
        },
        {
            "type": "text",
            "value": f"{float(dj['point_change']):.2f}",
            "x": 132,
            "y": 262,
            "fontSize": 24,
            "fill": get_color(convert_trend(dj["point_trend"]))
        },
        {
            "type": "image",
            "value": convert_trend(dj["point_trend"]),
            "x": 31,
            "y": 256,
            "fontSize": None,
            "height": None
        },
        {
            "type": "text",
            "value": f"{format_percent_change(dj['percent_change'], dj['point_trend'])}%",
            "x": 132,
            "y": 295,
            "fontSize": 24,
            "fill": get_color(convert_trend(dj["percent_trend"]))
        }
    ])

    # sp500_index
    sp = data["sp500_index"]
    output.extend([
        {
            "type": "text",
            "value": str(sp["current_value"]),
            "x": 134,
            "y": 385,
            "fontSize": 26,
            "fill": "#00000"
        },
        {
            "type": "text",
            "value": f"{float(sp['point_change']):.2f}",
            "x": 132,
            "y": 425,
            "fontSize": 24,
            "fill": get_color(convert_trend(sp["point_trend"]))
        },
        {
            "type": "image",
            "value": convert_trend(sp["point_trend"]),
            "x": 31,
            "y": 418,
            "fontSize": None,
            "height": None
        },
        {
            "type": "text",
            "value": f"{format_percent_change(sp['percent_change'], sp['point_trend'])}%",
            "x": 132,
            "y": 460,
            "fontSize": 24,
            "fill": get_color(convert_trend(sp["percent_trend"]))
        }
    ])

    # nasdaq_index
    nasdaq = data["nasdaq_index"]
    output.extend([
        {
            "type": "text",
            "value": str(nasdaq["current_value"]),
            "x": 275,
            "y": 226,
            "fontSize": 26,
            "fill": "#00000"
        },
        {
            "type": "text",
            "value": f"{float(nasdaq['point_change']):.2f}",
            "x": 274,
            "y": 262,
            "fontSize": 24,
            "fill": get_color(convert_trend(nasdaq["point_trend"]))
        },
        {
            "type": "image",
            "value": convert_trend(nasdaq["point_trend"]),
            "x": 177,
            "y": 256,
            "fontSize": None,
            "height": None
        },
        {
            "type": "text",
            "value": f"{format_percent_change(nasdaq['percent_change'], nasdaq['point_trend'])}%",
            "x": 274,
            "y": 295,
            "fontSize": 24,
            "fill": get_color(convert_trend(nasdaq["percent_trend"]))
        }
    ])

    # sox_index
    sox = data["sox_index"]
    output.extend([
        {
            "type": "text",
            "value": str(sox["current_value"]),
            "x": 275,
            "y": 385,
            "fontSize": 26,
            "fill": "#00000"
        },
        {
            "type": "text",
            "value": f"{float(sox['point_change']):.2f}",
            "x": 274,
            "y": 425,
            "fontSize": 24,
            "fill": get_color(convert_trend(sox["point_trend"]))
        },
        {
            "type": "image",
            "value": convert_trend(sox["point_trend"]),
            "x": 177,
            "y": 418,
            "fontSize": None,
            "height": None
        },
        {
            "type": "text",
            "value": f"{format_percent_change(sox['percent_change'], sox['point_trend'])}%",
            "x": 274,
            "y": 460,
            "fontSize": 24,
            "fill": get_color(convert_trend(sox["percent_trend"]))
        }
    ])

    return output


def transform2(data):
    output = []

    # 預先固定好的 x, y
    positions = {
        "report_date": (48, 57),
        "strong_stocks": [
            {
                "category_name": (85, 168),
                "percent_change": (93, 203),
                "percent_trend": (18, 193)
            },
            {
                "category_name": (85, 298),
                "percent_change": (93, 335),
                "percent_trend": (18, 326)
            },
            {
                "category_name": (85, 430),
                "percent_change": (93, 463),
                "percent_trend": (18, 456)
            }
        ],
        "weak_stocks": [
            {
                "category_name": (225, 168), #22
                "percent_change": (247, 201), #27
                "percent_trend": (175, 193)
            },
            {
                "category_name": (225, 298),
                "percent_change": (247, 333),
                "percent_trend": (175, 326)
            },
            {
                "category_name": (225, 430),
                "percent_change": (247, 463),
                "percent_trend": (175, 456)
            }
        ]
    }

    def convert_trend(trend):
        return "us_up" if trend == "up" else "us_down"

    # report_date
    output.append({
        "type": "text",
        "value": data["report_date"],
        "x": positions["report_date"][0],
        "y": positions["report_date"][1],
        "fontSize": 22,
        "fill": "#00367c"
    })

    def add_stock_info(stock_list, position_list, color):
        for stock, pos in zip(stock_list, position_list):
            output.append({
                "type": "text",
                "value": stock["category_name"],
                "x": pos["category_name"][0],
                "y": pos["category_name"][1],
                "fontSize": 22,
                "fill": color
            })
            output.append({
                "type": "text",
                "value": stock["percent_change"],
                "x": pos["percent_change"][0],
                "y": pos["percent_change"][1],
                "fontSize": 27,
                "fill": color
            })
            if stock["category_name"] != "NA":
                output.append({
                    "type": "image",
                    "value": convert_trend(stock["percent_trend"]),
                    "x": pos["percent_trend"][0],
                    "y": pos["percent_trend"][1],
                    "fontSize": None,
                    "height": None
                })


    add_stock_info(data.get("strong_stocks", []), positions["strong_stocks"], "#02b317")
    add_stock_info(data.get("weak_stocks", []), positions["weak_stocks"], "#da0000")


    return output

def transform4(news_data):
    output = []

    # 正常位置座標
    positions = [
        {"content": (22, 113), "source": (68, 98)},
        {"content": (22, 223), "source": (68, 205)},
        {"content": (22, 328), "source": (68, 310)},
        {"content": (22, 434), "source": (68, 416)}
    ]

    # NA 資料專用位置座標（只需 content）
    na_positions = [
        {"content": (600, 100)},
        {"content": (600, 180)},
        {"content": (600, 260)},
        {"content": (600, 340)}
    ]

    # 補滿到 4 則
    while len(news_data) < 4:
        news_data.append({
            "processed_title": "NA",
            "source": None
        })

    for idx in range(4):
        item = news_data[idx]
        is_na = item.get("processed_title") == "NA"

        if is_na:
            pos = na_positions[idx]
            output.append({
                "type": "text",
                "value": "NA",
                "x": pos["content"][0],
                "y": pos["content"][1],
                "fontSize": 22,
                "fill": "#00000"
            })
        else:
            pos = positions[idx]
            output.append({
                "type": "text",
                "value": item.get("processed_title", ""),
                "x": pos["content"][0],
                "y": pos["content"][1],
                "fontSize": 23,
                "fill": "#00000"
            })
            if item.get("source"):
                output.append({
                    "type": "text",
                    "value": item.get("source", ""),
                    "x": pos["source"][0],
                    "y": pos["source"][1],
                    "fontSize": 21,
                    "fill": "#FFFFFF"
                })

    return output




import os
import pandas as pd
import requests
from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Tuple

class StockAnalysis(BaseModel):
    has_stock: bool = Field(..., description="判斷是否有匹配到股票")
    url: str = Field(..., description="匹配到的股票的URL，找不到則返回空字符串")

# def get_xlsx_data(region: str = "台灣") -> str:
#     """
#     讀取資料夾中的 CSV 文件並返回篩選後的字符串
    
#     Args:
#         region (str): 要返回的區域資料，可選 "台灣" 或 "美國"
        
#     Returns:
#         str: 篩選後的CSV內容字符串
#     """
#     try:
#         base_dir = "/home/<USER>/ai_env/sam/project/stock_img/daily_data/"
#         if not os.path.exists(base_dir):
#             print(f"Base directory not found: {base_dir}")
#             return ""
        
#         current_date = datetime.now().strftime("%Y%m%d")
#         data_dir = os.path.join(base_dir, current_date)
        
#         if not os.path.exists(data_dir):
#             date_dirs = [d for d in os.listdir(base_dir) 
#                          if os.path.isdir(os.path.join(base_dir, d)) and d.isdigit() and len(d) == 8]
#             if date_dirs:
#                 latest_date = max(date_dirs)
#                 data_dir = os.path.join(base_dir, latest_date)
#                 print(f"Using latest date directory: {latest_date}")
#             else:
#                 print("No valid date directories found")
#                 return ""
        
#         csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
#         if not csv_files:
#             print("No CSV files found")
#             return ""
        
#         file_path = os.path.join(data_dir, csv_files[0])
#         print(f"Reading CSV file: {file_path}")
        
#         # 直接讀取文件內容為字符串
#         try:
#             with open(file_path, 'r', encoding='utf-8') as file:
#                 content = file.read()
#         except UnicodeDecodeError:
#             try:
#                 with open(file_path, 'r', encoding='big5') as file:
#                     content = file.read()
#             except:
#                 with open(file_path, 'r', encoding='cp950') as file:
#                     content = file.read()
        
#         # 按行分割並篩選
#         lines = content.strip().split('\n')
#         filtered_lines = []
        
#         for line in lines:
#             if not line.strip():  # 跳過空行
#                 continue
                
#             # 分割CSV行（考慮到可能有引號包含的逗號）
#             parts = []
#             current_part = ""
#             in_quotes = False
            
#             for char in line:
#                 if char == '"':
#                     in_quotes = not in_quotes
#                 elif char == ',' and not in_quotes:
#                     parts.append(current_part.strip())
#                     current_part = ""
#                     continue
#                 current_part += char
            
#             if current_part:
#                 parts.append(current_part.strip())
            
#             # 確保至少有4個部分（日期、區域、空列、標題）
#             if len(parts) < 4:
#                 continue
                
#             line_region = parts[1].strip()  # 第二個欄位是區域
#             title = parts[3].strip()        # 第四個欄位是標題
            
#             # 篩選條件：
#             # 1. 區域符合要求
#             # 2. 標題不包含"KGI語音精華"（整筆資料都不要）
#             if line_region == region and "KGI語音精華" not in title:
#                 filtered_lines.append(line)
        
#         result = '\n'.join(filtered_lines)
#         print(f"Filtered {len(filtered_lines)} lines for region: {region}")
        
#         return result
        
#     except Exception as e:
#         print(f"Error reading CSV file: {e}")
#         return ""
def get_xlsx_data(region: str = "台灣") -> str:
    """
    讀取資料夾中的 CSV 文件並返回篩選後的字符串
    只保留：日期、區域、股票名稱、URL 四個欄位
    
    Args:
        region (str): 要返回的區域資料，可選 "台灣" 或 "美國"
        
    Returns:
        str: 篩選後的CSV內容字符串
    """
    try:
        base_dir = "/home/<USER>/ai_env/sam/project/stock_img/daily_data/"
        if not os.path.exists(base_dir):
            print(f"Base directory not found: {base_dir}")
            return ""
        
        current_date = datetime.now().strftime("%Y%m%d")
        data_dir = os.path.join(base_dir, current_date)
        
        if not os.path.exists(data_dir):
            date_dirs = [d for d in os.listdir(base_dir) 
                         if os.path.isdir(os.path.join(base_dir, d)) and d.isdigit() and len(d) == 8]
            if date_dirs:
                latest_date = max(date_dirs)
                data_dir = os.path.join(base_dir, latest_date)
                print(f"Using latest date directory: {latest_date}")
            else:
                print("No valid date directories found")
                return ""
        
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        if not csv_files:
            print("No CSV files found")
            return ""
        
        file_path = os.path.join(data_dir, csv_files[0])
        print(f"Reading CSV file: {file_path}")
        
        # 直接讀取文件內容為字符串
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='big5') as file:
                    content = file.read()
            except:
                with open(file_path, 'r', encoding='cp950') as file:
                    content = file.read()
        
        # 按行分割並篩選
        lines = content.strip().split('\n')
        filtered_lines = []
        
        for line in lines:
            if not line.strip():  # 跳過空行
                continue
                
            # 分割CSV行（考慮到可能有引號包含的逗號）
            parts = []
            current_part = ""
            in_quotes = False
            
            for char in line:
                if char == '"':
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    parts.append(current_part.strip())
                    current_part = ""
                    continue
                current_part += char
            
            if current_part:
                parts.append(current_part.strip())
            
            # 確保至少有足夠的欄位
            if len(parts) < 4:  # 至少需要4個欄位
                continue
            
            # 檢查是否包含要排除的內容
            if "KGI語音精華" in line:
                continue
                
            # 提取需要的欄位
            date = parts[0].strip()           # 日期 (第1欄)
            line_region = parts[1].strip()    # 區域 (第2欄)
            url = parts[-1].strip()           # URL (最後一欄)
            
            # 從第5欄開始到倒數第2欄，收集所有股票名稱
            stock_names = []
            for i in range(4, len(parts)-1):  # 從第5欄到倒數第2欄
                if parts[i].strip():
                    stock_names.append(parts[i].strip())
            
            # 如果沒有股票名稱，跳過這筆資料
            if not stock_names:
                continue
                
            stock_name = ", ".join(stock_names)  # 用逗號分隔多個股票名稱
            
            # 篩選條件：區域符合要求且股票名稱不為空
            if line_region == region and stock_name:
                # 重新組合成CSV格式，只保留4個欄位
                filtered_line = f"{date},{line_region},{stock_name},{url}"
                filtered_lines.append(filtered_line)
        
        result = '\n'.join(filtered_lines)
        print(f"Filtered {len(filtered_lines)} lines for region: {region}")
        print("Output format: 日期,區域,股票名稱,URL")
        
        return result
        
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return ""

def check_url_alive(url: str) -> bool:
    """檢查 URL 是否存活"""
    try:
        response = requests.head(url, timeout=10)
        return response.status_code == 200
    except:
        return False

def find_stock_in_xlsx(stock_symbol: str, stock_name: str, df: pd.DataFrame) -> str:
    """在 Excel 數據中查找股票，返回對應的 URL"""
    if df.empty:
        return ""
    
    # 檢查是否有相關列名
    possible_columns = ['symbol', 'code', 'stock_code', 'ticker', 'name', 'company', 'url', 'link']
    
    for col in df.columns:
        col_lower = str(col).lower()

        if any(keyword in col_lower for keyword in ['symbol', 'code', 'ticker']):
            # 在股票代號列中查找
            mask = df[col].astype(str).str.upper().str.contains(stock_symbol.upper(), na=False)
            if mask.any():
                # 找到匹配的行，返回對應的 URL
                matched_row = df[mask].iloc[0]
                for url_col in df.columns:
                    if 'url' in url_col.lower() or 'link' in url_col.lower():
                        return str(matched_row[url_col])
                return ""
        
        elif any(keyword in col_lower for keyword in ['name', 'company']):
            # 在公司名稱列中查找
            if stock_name:
                mask = df[col].astype(str).str.contains(stock_name, case=False, na=False)
                if mask.any():
                    matched_row = df[mask].iloc[0]
                    for url_col in df.columns:
                        if 'url' in url_col.lower() or 'link' in url_col.lower():
                            return str(matched_row[url_col])
                    return ""
    
    return ""


def analyze_article_with_llm(article: Dict[str, Any], xlsx_data: str, llm_instance) -> Tuple[int, str]:
    """使用 LLM 分析文章並返回 image_index 和 url"""
    title = article.get('title', '')
    summaries = article.get('summary', [])
    summary_text = ' '.join([s.get('summary', '') for s in summaries])
    
    # 先分析標題
    title_prompt = f"""
    標題: {title}
    
    要查找的資料 :
    {xlsx_data}
    
    步驟 :
    1. 先判斷標題當中有沒有出現美股(要出現美股的名稱或簡稱才算，例如特斯拉或輝達，只出現產業類別不算)
    2. 如果標題沒有出現美股，直接返回 {{"has_stock":false,"url":""}}
    3. 如果標題中有出現美股，則再根據該股票與要查找的資料進行比對，看當中有沒有要查找的股票
        - 如果有且配對到一個，則返回 {{"has_stock":true,"url":"配對到的url"}}，如果同時配對到多個，則使用最新的那則並返回 {{"has_stock":true,"url":"配對到的url"}}
        - 如果沒有，則直接返回 {{"has_stock":false,"url":""}}
    
    配對規則 :
        1. 根據找到的美股，在要查找的資料當中找到相同的美股才算配對成功
        2. 即使出現相同的關鍵字例如 AI ，但如果沒有辦法匹配到相同美股，那就不算匹配成功，匹配成功的例子例如 輝達 匹配到 輝達、nvidia、NVDA之類的。
    """

    title_messages = [{"role": "user", "content": title_prompt}]
    
    try:
        title_analysis = llm_instance.chat_json(title_messages)
        print(f"Title analysis result: has_stock={title_analysis.has_stock}, url='{title_analysis.url}'")
        
        if title_analysis.has_stock and title_analysis.url:
            print("Found matching stock in Excel via title, using image_index 0")
            return 0, title_analysis.url
        
    except Exception as e:
        print(f"Error in title analysis: {e}")
    
    # 如果標題沒有找到，分析摘要
    if summary_text.strip():
        summary_prompt = f"""
        摘要: {summary_text}
    
        要查找的資料 :
        {xlsx_data}
        
        步驟 :
        1. 先判斷摘要當中有沒有出現美股(要出現美股的名稱或簡稱才算，例如特斯拉或輝達，只出現產業類別不算)
        2. 如果摘要沒有出現美股，直接返回 {{"has_stock":false,"url":""}}
        3. 如果摘要中有出現美股，則再根據該股票與要查找的資料進行比對，看當中有沒有要查找的股票
            - 如果有且配對到一個，則返回 {{"has_stock":true,"url":"配對到的url"}}，如果同時配對到多個，則使用最新的那則並返回 {{"has_stock":true,"url":"配對到的url"}}
            - 如果沒有，則直接返回 {{"has_stock":false,"url":""}}
        
        配對規則 :
        1. 根據找到的美股，在要查找的資料當中找到相同的美股才算配對成功
        2. 即使出現相同的關鍵字例如 AI ，但如果沒有辦法匹配到相同美股，那就不算匹配成功，匹配成功的例子例如 輝達 匹配到 輝達、nvidia、NVDA之類的。
        """
        
        summary_messages = [{"role": "user", "content": summary_prompt}]
        
        try:
            summary_analysis = llm_instance.chat_json(summary_messages)
            print(f"Summary analysis result: has_stock={summary_analysis.has_stock}, url='{summary_analysis.url}'")
            print()
            

            
            if summary_analysis.has_stock and summary_analysis.url:
                print("Found matching stock in Excel via summary, using image_index 0")
                return 0, summary_analysis.url
            
        except Exception as e:
            print(f"Error in summary analysis: {e}")
    
    # 都沒有找到匹配的美股，使用原始文章URL
    print("No US stock found or no matching data in Excel, using image_index 1")
    return 1, article.get('url', '')



def transform5(articles, OPENAI_API_KEY):
    TITLE_POSITION = {"x": 160, "y": 128, "fontSize": 32, "fill": "#000000"}
    SUMMARY_POSITIONS = [
        {"x": 34, "y": 215, "fontSize": 20, "fill": "#00000"},
        {"x": 34, "y": 313, "fontSize": 20, "fill": "#00000"},
        {"x": 34, "y": 409, "fontSize": 20, "fill": "#00000"},
    ]
    
    # 初始化 LLM
    llm = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY, response_model=StockAnalysis)
    
    # 獲取 Excel 數據（自動尋找資料夾中的 .xlsx 檔案）
    xlsx_data = get_xlsx_data(region="美國")
    
    result = []
    result_urls = []
    
    print(f"articles: {articles}")
    
    for article in articles:
        # 使用 LLM 分析文章
        image_index, article_url = analyze_article_with_llm(article, xlsx_data, llm)
        
        if image_index == 1 :
            try:
                title = article.get('title', '')
                summaries = article.get('summary', [])
                class StockShort(BaseModel):
                    stock_short: str = Field(..., description="美股代碼")
                    
                stock_short_llm = OpenAiLLM(model_name="o3", api_key=OPENAI_API_KEY, response_model=StockShort)
                
                stock_short_prompt = f"""
                標題 : {title}
                摘要 : {summaries}
                
                指令 : 
                    1. 判斷標題或摘要有沒有出現美股
                    2. 如果兩個都有出現美股，則優先使用標題的美股
                    3. 將要用的該美股轉成股票代碼，例如 : 
| 中文公司名稱          | 美股代碼  |
| --------------- | ----- |
| 蘋果公司            | AAPL  |
| 微軟公司            | MSFT  |
| 亞馬遜公司           | AMZN  |
| Alphabet（谷歌母公司、Google） | GOOGL |
| Meta（臉書母公司）     | META  |
| 輝達公司            | NVDA  |
| 特斯拉公司           | TSLA  |
| 伯克希爾·哈撒韋        | BRK.B |
| 台積電（美國掛牌）       | TSM   |
| 美國銀行            | BAC   |
| 摩根大通            | JPM   |
| Visa公司          | V     |
| Mastercard公司    | MA    |
| 英特爾公司           | INTC  |
| 高通公司            | QCOM  |
| 波音公司            | BA    |
| 可口可樂公司          | KO    |
| 百事可樂公司          | PEP   |
| 強生公司            | JNJ   |
| 家得寶公司           | HD    |
| 麥當勞公司           | MCD   |
| 星巴克公司           | SBUX  |
| Netflix公司       | NFLX  |
| Adobe公司         | ADBE  |
| PayPal公司        | PYPL  |
| 思科系統公司       | CSCO  |
| 甲骨文公司        | ORCL  |
| Salesforce公司 | CRM   |
| Uber公司       | UBER  |
| Lyft公司       | LYFT  |
| Spotify公司    | SPOT  |
| Zoom公司       | ZM    |
| Airbnb公司     | ABNB  |
| Snowflake公司  | SNOW  |
| Palantir公司   | PLTR  |
| 阿斯麥（光刻機）     | ASML  |
| 艾克森美孚公司      | XOM   |
| 雪佛龍公司        | CVX   |
| 費森美孚（醫療器材）   | MDT   |
| 輝瑞公司         | PFE   |
| 莫德納公司        | MRNA  |
| 禮來公司         | LLY   |
| 默克公司         | MRK   |
| CVS健康公司      | CVS   |
| 華特迪士尼公司      | DIS   |
| Comcast公司    | CMCSA |
| AT&T公司      | T     |
| Verizon公司    | VZ    |
| Target公司     | TGT   |
| 沃爾瑪公司        | WMT   |
| 好市多公司        | COST  |
| eBay公司       | EBAY  |
| 阿里巴巴（美國掛牌）   | BABA  |
| 百度（美國掛牌）     | BIDU  |
| 京東（美國掛牌）     | JD    |
| 小鵬汽車（美國掛牌）   | XPEV  |
| 理想汽車（美國掛牌）   | LI    |
| 蔚來汽車（美國掛牌）   | NIO   |
| 美光科技       | MU   |
| 超微半導體（AMD） | AMD  |
| 英偉達（輝達）    | NVDA |
| 博通公司       | AVGO |
| 德州儀器       | TXN  |
| 安森美半導體     | ON   |
| Marvell科技  | MRVL |
| 高盛公司      | GS    |
| 摩根士丹利     | MS    |
| 花旗銀行      | C     |
| 美國運通      | AXP   |
| 富國銀行      | WFC   |
| 查理斯·施瓦布   | SCHW  |
| 安聯保險（ADR） | ALIZY |
| 美國國際集團    | AIG   |
| Nike公司         | NKE   |
| Adidas（美股ADR）  | ADDYY |
| Lululemon公司    | LULU  |
| 梅西百貨           | M     |
| 百思買公司          | BBY   |
| 克羅格超市          | KR    |
| DOLLAR TREE    | DLTR  |
| DOLLAR GENERAL | DG    |
| 通用汽車         | GM   |
| 福特汽車         | F    |
| 電動車Fisker    | FSR  |
| Lucid Motors | LCID |
| Rivian公司     | RIVN |
| 康菲石油          | COP  |
| Halliburton公司 | HAL  |
| 斯倫貝謝公司        | SLB  |
| NextEra能源     | NEE  |
| 道達爾能源（ADR）    | TTE  |
| 安進公司       | AMGN |
| 丹納赫公司      | DHR  |
| 吉利德科學      | GILD |
| Biogen公司   | BIIB |
| Illumina公司 | ILMN |
| 比特幣 | BTC |
| 新思科技 | SNPS |
| 美國航空             | AAL  |
| 達美航空             | DAL  |
| 聯合航空             | UAL  |
| 西南航空             | LUV  |
| Expedia集團        | EXPE |
| Booking Holdings | BKNG |
| 皇家加勒比郵輪          | RCL  |
| 挪威郵輪             | NCLH |
| 嘉年華郵輪            | CCL  |
| 寶潔公司（P&G）  | PG   |
| 金百利克拉克      | KMB  |
| 高露潔-棕欖      | CL   |
| 漢斯牌（亨氏）食品   | KHC  |
| Clorox（漂白水） | CLX  |
| 蒙德里茲國際（零食）  | MDLZ |
| 露華濃         | REV  |
| 卡特彼勒公司      | CAT  |
| 聯合技術公司      | UTX  |
| 通用電氣        | GE   |
| 洛克希德·馬丁     | LMT  |
| 雷神技術公司      | RTX  |
| 霍尼韋爾        | HON  |
| Deere（約翰迪爾） | DE   |
| 3M公司        | MMM  |
| 史丹利百得       | SWK  |
| Chegg學習平台     | CHGG |
| Coursera課程平台  | COUR |
| Skillsoft技能學習 | SKIL |
| 2U（大學合作平台）    | TWOU |
| Block（Square）公司 | SQ   |
| SoFi公司          | SOFI |
| Affirm分期付款平台    | AFRM |
| Vertex製藥        | VRTX |
| Seagen（癌症藥物）    | SGEN |
| Incyte公司        | INCY |
| Moderna（疫苗）     | MRNA |
| Regeneron再生元醫藥  | REGN |
| Bausch Health公司 | BHC  |
| BioMarin公司      | BMRN |
| Alnylam製藥       | ALNY |
| Sarepta治療公司     | SRPT |
| Shopify電商平台  | SHOP |
| Etsy手工藝平台    | ETSY |
| Chewy寵物電商    | CHWY |
| Wayfair家具電商  | W    |
| Carvana二手車平台 | CVNA |
| e.l.f.美容產品   | ELF  |
| FedEx公司      | FDX  |
| UPS（聯邦快遞）    | UPS  |
| ServiceNow公司    | NOW  |
| Workday（人資雲）    | WDAY |
| Atlassian（Jira） | TEAM |
| Twilio公司        | TWLO |
| DocuSign電子簽署    | DOCU |
| Dropbox雲端儲存     | DBX  |
| Box雲端儲存         | BOX  |
| Asana工作管理平台     | ASAN |
| EA藝電（FIFA、Apex）  | EA        |
| Take-Two（GTA母公司） | TTWO      |
| Roblox遊戲平台       | RBLX      |
| Unity遊戲引擎        | U         |
| 任天堂（美股ADR）       | NTDOY     |
| 索尼（美股ADR）        | SONY      |
| Boston Scientific波士頓科學 | BSX  |
| Stryker醫材              | SYK  |
| Becton Dickinson醫療設備   | BDX  |
| ResMed睡眠設備             | RMD  |
| Dexcom血糖監測             | DXCM |
| Zimmer Biomet人工關節      | ZBH  |
| 華納兄弟探索              | WBD  |
| 派拉蒙環球               | PARA |
| 福斯公司（Fox）           | FOXA |
| Roku串流平台            | ROKU |
| Pinterest社群平台       | PINS |
| Snap聊天平台            | SNAP |
| Match Group（Tinder） | MTCH |
| Freeport-McMoRan銅礦  | FCX  |
| Newmont金礦           | NEM  |
| Albemarle鋰電材料       | ALB  |
| MP Materials稀土      | MP   |
| International Paper | IP   |
| Steel Dynamics（鋼鐵）  | STLD |


                4. 根據指定格式回答，例如 {{"stock_short":"股票代碼，如果沒有美股則為空"}}

                """
                
                ss_messages = [{"role": "user", "content": stock_short_prompt}]

                stock_ans = stock_short_llm.chat_json(ss_messages)
                print(f"find stock short : {stock_ans.stock_short}")
                us_url = f"https://www.kgieworld.com.tw/subbrokerage/stock.html?area=US&code={stock_ans.stock_short}"
                if stock_ans.stock_short!="" and check_url_alive(us_url):
                    article_url = us_url
                else :
                    image_index = 2
            except :
                image_index = 2
                    
        
        entry = {
            "image_index": image_index,
            "data": []
        }
        
        # 固定位置加入 title
        entry["data"].append({
            "type": "text",
            "value": article.get("title", ""),
            **TITLE_POSITION
        })

        # 固定位置加入每個 summary（最多三個）
        summaries = article.get("summary", [])
        for i, summary_item in enumerate(summaries[:3]):
            position = SUMMARY_POSITIONS[i]
            entry["data"].append({
                "type": "text",
                "value": summary_item.get("summary", "") if isinstance(summary_item, dict) else str(summary_item),
                **position
            })
        
        result.append(entry)
        result_urls.append(article_url)
    
    return result, result_urls



def us1(lines, OPENAI_API_KEY):
    if not lines:
        return JSONResponse(status_code=400, content={"error": "File is empty."})

    first_line = lines[0]

    match = re.search(r"\b(\d{4})-(\d{1,2})-(\d{1,2})\b", first_line)
    if not match:
        return JSONResponse(status_code=400, content={"error": "Date not found in first line."})

    # 解析日期
    year, month, day = match.groups()
    current_txt_date = f"{year}-{int(month):02d}-{int(day):02d}"

    US1 = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY, response_model=UsIndex)
    
    messages = [
        {
            "role": "user",
            "content": US_INDEX.format(
                current_date=current_txt_date,
                data=lines
            )
        }
    ]
    result = US1.chat_json(messages)
    result = transform1(result.model_dump())

    return [result]



def us2(lines, OPENAI_API_KEY):
    if not lines:
        return JSONResponse(status_code=400, content={"error": "File is empty."})

    Sector = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY, response_model=StrongWeekStack)
    result = Sector.chat_json([
        {"role": "user", "content": Sector_prompt.format(data=lines) }
    ])
    
    result = result.model_dump()
    result = transform2(result)
    return [result]

# def us3(docs_text):
#     # 擷取段落區間的 helper
#     def extract_section(text, start_kw, end_kw):
#         try:
#             start = text.index(start_kw) + len(start_kw)
#             end = text.index(end_kw)
#             return text[start:end].strip()
#         except ValueError:
#             return ''

#     # 擷取美股收盤
#     market_summary = extract_section(docs_text, "美股收盤方面，", "主要指數及類股漲跌方面，")

#     # 擷取重要經濟數據區間
#     economic_raw = extract_section(docs_text, "重要經濟數據方面，", "企業財報方面，")
#     economic_sentences = [line.strip() for line in economic_raw.splitlines() if line.strip()]
#     economic_summary = '，'.join(economic_sentences).rstrip('，') + '。' if economic_sentences else ''

#     # 組合結果
#     result = (
#         f"[美股收盤]{market_summary.strip()}\n"
#         f"[重要經濟數據]{economic_summary}"
#     )

#     return [[{
#         'type': 'text',
#         'value': result,
#         'x': 20,
#         'y': 84,
#         'fontSize': 22,
#         'fill': '#00000'
#     }]]

def us3(docs_text, OPENAI_API_KEY):
    
    class Extract(BaseModel):
        close_data: str = Field(..., description="美股收盤的完整內容")
        important_static: str = Field(..., description="重要經濟數據的完整內容")

    instruct_prompt = """
    你是一個專業的金融分析師，請根據以下文本內容，提取美股收盤和重要經濟數據的摘要。
    文本內容如下：
    {data}
    請將美股收盤的內容提取為 close_data，重要經濟數據的內容提取為 important_static。
    回傳格式為 JSON，包含 close_data 和 important_static 兩個欄位
    並且每個欄位的內容都要完整且清晰，不可隨意更改。
    請依照XXX方面的區間來去判斷是否是該方面的內容，不要抓到其他範圍的內容。
    例如: 美股收盤的內容只抓取 美股收盤方面~ 下一個方面的資料。 重要經濟數據則是判斷下一個小標題之前的內容。 但不要抓到標題。
    """
    
    Sector = OpenAiLLM(model_name="o3", api_key=OPENAI_API_KEY, response_model=Extract)
    result = Sector.chat_json([
        {"role": "user", "content": instruct_prompt.format(data=docs_text) }
    ])
    
    result = result.model_dump()
    
    # 組合結果
    result = (
        f"[美股收盤] {result.get("close_data","")}\n"
        f"[重要經濟數據] {result.get("important_static","")}"
    )

    return [[{
        'type': 'text',
        'value': result,
        'x': 20,
        'y': 84,
        'fontSize': 22,
        'fill': '#00000'
    }]]


   
class ChangeLineTitleModel(BaseModel):
    title: str = Field(..., description="換行後的標題")    


async def us4_5(search_contents: list[str], OPENAI_API_KEY: str, first_date: str):
    import opencc
    import re

    converter = opencc.OpenCC('s2t')
    
    
    data = []
    for i in range(4):
        original = search_contents[i]
        match = re.search(r'^(.*?[。；])', original)

        if match:
            shortened = match.group(1)
        else:
            shortened = original

        search_contents[i] = shortened
        
        results = search_first_url(search_contents[i])
        if results:
            top = results[0]
        else:
            # 備用搜尋也要避免香港、中國新聞
            exclude_keywords = [
                # 港股相關
                '港股', '恆生', '恆指', '港交所', '港幣', '香港', 'HK', 'HSI',
                # 中國股市相關
                '上證', '深證', '滬深', 'A股', '上交所', '深交所', '中國大陸', '內地', '陸股',
                '中概股', '人民幣', '滬港通', '深港通', '科創板', '創業板', '新三板',
                # 其他亞洲市場
                '日經', '韓股', '台股', '新加坡', '泰股', '印度股市',
                # 明確的地區標識
                '中國', '大陸', '亞洲', '亞太', '東南亞'
            ]

            enhanced_query = f"{search_contents[i]} 美股"
            fallback_found = False

            # 簡化的備用搜尋，但過濾香港、中國新聞
            for fallback in search(enhanced_query, num_results=5, lang="zh-TW", advanced=True):
                if not fallback:
                    continue

                title = getattr(fallback, 'title', '')
                url = getattr(fallback, 'url', '')

                # 檢查是否包含香港、中國相關關鍵字（避免語意奇怪）
                china_hk_keywords = [
                    '港股', '恆生', '恆指', '港交所', '香港', '港元',
                    '上證', '深證', 'A股', '陸股', '中國股市', '人民幣',
                    '滬深', '創業板', '科創板', '港幣'
                ]

                if any(keyword in title or keyword in url for keyword in china_hk_keywords):
                    continue

                domain = urlparse(fallback.url).netloc
                top = {
                    'title': title or fallback.url,
                    'url': fallback.url,
                    'source': get_chinese_source(domain)
                }
                fallback_found = True
                break

            if not fallback_found:
                continue
        print(f"第{i}則")
        print()
        print(f"內容:{search_contents[i]}")
        print(top)
        data.append(top)
    
    def make_title_llm():
        return OpenAiLLM(
            model_name="o3",
            api_key=OPENAI_API_KEY,
            response_model=TitleModel
        )
        
    shorten_prompt = "將本新聞標題簡化並且要通順要有標點符號喔如果有斷句的話，約16字左右，並依照指定格式回傳"
    
    for item in data :
        original_title = item.get('title', '')
        item['title'] = re.sub(r'\s{2,}', ' ', original_title)
        shorten_title = make_title_llm().chat_json([{"role": "user", "content": shorten_prompt+f"\n新聞標題:{item.get('title', '')}"}])
        content = shorten_title.model_dump().get('title', item.get('title', ''))
        item['processed_title'] = content
    
    us4_results_transformed = transform4(data)
    us4_results_transformed.append({
                "type": "text",
                "value": first_date,
                "x": 113,
                "y": 51,
                "fontSize": 14,
                "fill": "#00367c"
            })
    
    for item in data :
        # 跳過 NA 項目
        if item.get('processed_title') == 'NA':
            item["summary"] = [{"summary": "無法獲取內容"}, {"summary": ""}, {"summary": ""}]
            continue

        final_title = item.get('processed_title', '')
        md = await crawl_content(item.get('url', ''))

        summary_llm = OpenAiLLM(
            model_name="o3",
            api_key=OPENAI_API_KEY,
            response_model=US_NEWS_SUM
        )

        if not md:
            print(f"無法抓取新聞內容，跳過此項目: {item.get('url', '')}")
            # 為這個項目創建一個空的摘要
            item["summary"] = [{"summary": "無法獲取內容"}, {"summary": ""}, {"summary": ""}]
            continue

        summary_resp = summary_llm.chat_json([
            {
                "role": "user",
                "content": f"""新聞標題:{item.get('processed_title', '')}\n新聞內容:{md}\n將本新聞內容請歸納出3個重點，每個重點約20個字左右，重點描述不要與新聞標題重覆，以及不使用文字加粗方式進行回答，要根據指定格式回覆\n"""
            }
        ])

        summary = summary_resp.model_dump().get("summaries", [])
        item["summary"] = summary
        
        class ChangeLineTitleModel(BaseModel):
            title: str = Field(..., description="換行後的標題")
        
        change_line_prompt = fr"""
        你的任務是將標題進行換行，以下是判斷步驟 :
        1. 先判斷標題是否已經根據語意進行換行
            a. 如果已經換行，則先判斷換行的行數，如果超過兩行，則重新判斷換行位置(因為最多只能兩行)
            b. 如果已經換行且不超過兩行，則判斷每行字數有沒有超過9個字元(中文一字算 1 個字，英文字母與數字是兩個字算 1 個)，如果超過，則直接返回原標題(保留空格，不使用換行符號\n)
            c. 如果還沒換行，則根據語意進行換行，最多兩行，每行不超過9個字元(使用\n進行換行並刪除空格)
        2. 根據指定格式回覆。
        
        原標題 : {item.get('processed_title', '')}
        """

        CHANGELINE_LLM = OpenAiLLM(model_name="o3", api_key=OPENAI_API_KEY, response_model=ChangeLineTitleModel)
        final_result = CHANGELINE_LLM.chat_json([{"role":"user","content":change_line_prompt}])

        final_title = final_result.model_dump().get('title', item.get('processed_title', ''))
        item['title'] = final_title
    
    final_result, urls = transform5(data, OPENAI_API_KEY)
    
    import json
    
    combined_results = []
    for result, image_url in zip(data, urls):
        combined_results.append({
            'title': result.get('title', ''),
            'news_url': result.get('url', ''),
            'url': image_url
        })

    # 建立目錄 YYYYMMDD
    date_str = datetime.now().strftime("%Y%m%d")
    base_dir = "/home/<USER>/ai_env/sam/project/stock_img/urls"
    target_dir = os.path.join(base_dir, date_str)
    os.makedirs(target_dir, exist_ok=True)

    # 存成 JSON
    file_path = os.path.join(target_dir, "us.json")
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(combined_results, f, ensure_ascii=False, indent=4)

    print(f"JSON saved to {file_path}")
    
    
    
    return [[us4_results_transformed], [final_result], urls]