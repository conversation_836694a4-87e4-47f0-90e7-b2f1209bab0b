#!/usr/bin/env python3
"""
測試企業URL修復的腳本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.taiex import get_tw_stock_url, analyze_article_with_llm
from utils.us import get_us_stock_url
from LLM import OpenAiLLM
from key import OPENAI_API_KEY
from utils.taiex import StockAnalysis as TWStockAnalysis
from utils.us import StockAnalysis as USStockAnalysis

def test_tw_stock_url_generation():
    """測試台股URL生成"""
    print("=== 測試台股URL生成 ===")
    
    test_cases = [
        "2330",
        "2317", 
        "2454",
        "2308",
        "TW2330",
        "(2330)",
        "2330 TT",
        "",
        None
    ]
    
    for code in test_cases:
        url = get_tw_stock_url(code)
        print(f"股票代碼: {code} -> URL: {url}")

def test_us_stock_url_generation():
    """測試美股URL生成"""
    print("\n=== 測試美股URL生成 ===")
    
    test_cases = [
        "AAPL",
        "MSFT",
        "NVDA", 
        "TSLA",
        "GOOGL",
        "",
        None
    ]
    
    for code in test_cases:
        url = get_us_stock_url(code)
        print(f"股票代碼: {code} -> URL: {url}")

def test_tw_stock_analysis():
    """測試台股分析邏輯"""
    print("\n=== 測試台股分析邏輯 ===")
    
    try:
        llm = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY, response_model=TWStockAnalysis)
        
        test_articles = [
            {
                'title': '台積電第三季財報超預期，AI晶片需求強勁',
                'summary': [{'summary': '台積電公布第三季財報，營收創新高'}],
                'url': 'https://example.com/news1'
            },
            {
                'title': '鴻海布局電動車，與特斯拉合作',
                'summary': [{'summary': '鴻海宣布進軍電動車市場'}],
                'url': 'https://example.com/news2'
            },
            {
                'title': '半導體產業展望，市場看好',
                'summary': [{'summary': '半導體產業整體表現良好'}],
                'url': 'https://example.com/news3'
            }
        ]
        
        for i, article in enumerate(test_articles):
            print(f"\n測試文章 {i+1}:")
            print(f"標題: {article['title']}")
            
            try:
                image_index, url = analyze_article_with_llm(article, "", llm)
                print(f"結果: image_index={image_index}, url={url}")
            except Exception as e:
                print(f"分析失敗: {e}")
                
    except Exception as e:
        print(f"初始化LLM失敗: {e}")

def test_us_stock_analysis():
    """測試美股分析邏輯"""
    print("\n=== 測試美股分析邏輯 ===")
    
    try:
        from utils.us import analyze_article_with_llm as us_analyze
        llm = OpenAiLLM(model_name="gpt-4o", api_key=OPENAI_API_KEY, response_model=USStockAnalysis)
        
        test_articles = [
            {
                'title': '蘋果發布新iPhone，市場反應熱烈',
                'summary': [{'summary': '蘋果公司發布最新iPhone系列'}],
                'url': 'https://example.com/news1'
            },
            {
                'title': '輝達AI晶片需求持續強勁',
                'summary': [{'summary': 'NVIDIA在AI領域表現優異'}],
                'url': 'https://example.com/news2'
            },
            {
                'title': '科技股整體表現良好',
                'summary': [{'summary': '科技類股普遍上漲'}],
                'url': 'https://example.com/news3'
            }
        ]
        
        for i, article in enumerate(test_articles):
            print(f"\n測試文章 {i+1}:")
            print(f"標題: {article['title']}")
            
            try:
                image_index, url = us_analyze(article, "", llm)
                print(f"結果: image_index={image_index}, url={url}")
            except Exception as e:
                print(f"分析失敗: {e}")
                
    except Exception as e:
        print(f"初始化LLM失敗: {e}")

if __name__ == "__main__":
    print("開始測試企業URL修復功能...")
    
    test_tw_stock_url_generation()
    test_us_stock_url_generation()
    
    # 注意：以下測試需要有效的OpenAI API Key
    if OPENAI_API_KEY and OPENAI_API_KEY != "your-api-key-here":
        test_tw_stock_analysis()
        test_us_stock_analysis()
    else:
        print("\n跳過LLM測試（需要有效的OpenAI API Key）")
    
    print("\n測試完成！")
